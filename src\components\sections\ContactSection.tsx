import React from 'react';
import { ContactSectionProps } from '@/types';
import { SectionContainer } from '@/components/layout';
import { 
  SectionHeader,
  Heading,
  BodyText,
  Button,
  Card,
  ContactCard,
  ContactSocialLinks
} from '@/components/ui';

/**
 * Contact section component with social links and contact information
 * Features contact cards, social media links, and call-to-action
 */
const ContactSection: React.FC<ContactSectionProps> = ({
  title = "Get in Touch",
  description = "Ready to bring your vision to life? Let's create something amazing together.",
  contactInfo = {
    email: "<EMAIL>",
    phone: "+****************",
    address: "Los Angeles, CA"
  },
  socialLinks = [
    { platform: 'instagram', url: 'https://instagram.com/perpixel' },
    { platform: 'twitter', url: 'https://twitter.com/perpixel' },
    { platform: 'linkedin', url: 'https://linkedin.com/company/perpixel' }
  ],
  className = '',
}) => {
  const contactMethods = [
    {
      title: "Email Us",
      description: contactInfo.email,
      icon: "📧",
      action: () => window.open(`mailto:${contactInfo.email}`)
    },
    {
      title: "Call Us",
      description: contactInfo.phone,
      icon: "📞",
      action: () => window.open(`tel:${contactInfo.phone}`)
    },
    {
      title: "Visit Us",
      description: contactInfo.address,
      icon: "📍",
      action: () => window.open(`https://maps.google.com/?q=${encodeURIComponent(contactInfo.address!)}`)
    }
  ];

  return (
    <SectionContainer background="default" className={className}>
      {/* Section Header */}
      <div className="text-center mb-16">
        <SectionHeader color="dark" className="mb-6">
          {title}
        </SectionHeader>
        
        {description && (
          <div className="max-w-2xl mx-auto">
            <BodyText size="lg" color="muted" className="text-center">
              {description}
            </BodyText>
          </div>
        )}
      </div>

      {/* Main Contact Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
        {/* Left side - Contact Information */}
        <div className="space-y-8">
          <div>
            <Heading color="dark" className="mb-6">
              Contact Information
            </Heading>
            
            <div className="space-y-6">
              {contactMethods.map((method, index) => (
                <ContactCard
                  key={index}
                  title={method.title}
                  description={method.description}
                  icon={<span className="text-2xl">{method.icon}</span>}
                  action={{
                    label: "Contact",
                    onClick: method.action
                  }}
                />
              ))}
            </div>
          </div>

          {/* Social Media Links */}
          <div>
            <Heading color="dark" className="mb-6 text-lg">
              Follow Us
            </Heading>
            <ContactSocialLinks links={socialLinks} />
          </div>
        </div>

        {/* Right side - Contact Form or CTA */}
        <div className="space-y-8">
          <div>
            <Heading color="dark" className="mb-6">
              Start Your Project
            </Heading>
            
            {/* Contact CTA Card */}
            <Card variant="contact" padding="lg" className="relative overflow-hidden min-h-[300px]">
              <div className="space-y-6">
                <div className="flex items-start justify-between">
                  <BodyText className="text-sm leading-relaxed">
                    Have some<br />questions?
                  </BodyText>
                  <div className="text-3xl">💡</div>
                </div>
                
                <div className="space-y-4">
                  <BodyText className="leading-relaxed">
                    Ready to transform your ideas into stunning digital experiences? 
                    Let's discuss your project and create something extraordinary together.
                  </BodyText>
                  
                  <div className="space-y-3">
                    <Button 
                      variant="primary" 
                      size="lg"
                      className="w-full"
                      onClick={() => window.open(`mailto:${contactInfo.email}`)}
                    >
                      Start a Project
                    </Button>
                    
                    <Button 
                      variant="secondary" 
                      size="md"
                      className="w-full"
                      onClick={() => window.open(`tel:${contactInfo.phone}`)}
                    >
                      Schedule a Call
                    </Button>
                  </div>
                </div>
              </div>
              
              {/* Decorative element */}
              <div className="absolute bottom-6 right-6 opacity-20">
                <span className="text-4xl">✨</span>
              </div>
            </Card>
          </div>

          {/* Additional Info Card */}
          <Card variant="introduction" padding="lg">
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="text-2xl">⏰</div>
                <Heading className="text-lg">Response Time</Heading>
              </div>
              
              <BodyText color="muted" className="text-sm">
                We typically respond to all inquiries within 24 hours. 
                For urgent projects, feel free to call us directly.
              </BodyText>
              
              <div className="pt-4 border-t border-gray-200">
                <BodyText className="text-sm font-medium">
                  Business Hours: Monday - Friday, 9 AM - 6 PM PST
                </BodyText>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Bottom CTA Section */}
      <div className="text-center bg-secondary-peach rounded-3xl p-12">
        <div className="max-w-3xl mx-auto">
          <Heading color="dark" className="mb-4 text-2xl lg:text-3xl">
            Ready to Create Something Amazing?
          </Heading>
          
          <BodyText size="lg" color="muted" className="mb-8">
            Join hundreds of satisfied clients who have transformed their vision into reality with PerPixel.
          </BodyText>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              variant="primary" 
              size="lg"
              onClick={() => window.open(`mailto:${contactInfo.email}`)}
            >
              Get Started Today
            </Button>
            
            <Button 
              variant="secondary" 
              size="lg"
              href="/portfolio"
            >
              View Our Work
            </Button>
          </div>
        </div>
      </div>
    </SectionContainer>
  );
};

export default ContactSection;

// Simplified contact section variant
export const SimpleContactSection: React.FC<{
  email: string;
  phone?: string;
  className?: string;
}> = ({ email, phone, className = '' }) => (
  <SectionContainer background="dark" className={className}>
    <div className="text-center">
      <SectionHeader color="light" className="mb-8">
        Let's Work Together
      </SectionHeader>
      
      <div className="space-y-6">
        <BodyText size="lg" color="light">
          Ready to start your next project?
        </BodyText>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button 
            variant="cta" 
            size="lg"
            onClick={() => window.open(`mailto:${email}`)}
          >
            Send Email
          </Button>
          
          {phone && (
            <Button 
              variant="secondary" 
              size="lg"
              onClick={() => window.open(`tel:${phone}`)}
            >
              Call Now
            </Button>
          )}
        </div>
      </div>
    </div>
  </SectionContainer>
);

// Newsletter signup section
export const NewsletterSection: React.FC<{
  title?: string;
  description?: string;
  className?: string;
}> = ({ 
  title = "Stay Updated", 
  description = "Get the latest updates on our projects and design insights.",
  className = '' 
}) => (
  <SectionContainer background="primary" className={className}>
    <div className="max-w-2xl mx-auto text-center">
      <SectionHeader color="light" className="mb-4">
        {title}
      </SectionHeader>
      
      <BodyText color="light" className="mb-8">
        {description}
      </BodyText>
      
      <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
        <input
          type="email"
          placeholder="Enter your email"
          className="flex-1 px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-white/50 outline-none"
        />
        <Button variant="secondary" size="md">
          Subscribe
        </Button>
      </div>
    </div>
  </SectionContainer>
);
