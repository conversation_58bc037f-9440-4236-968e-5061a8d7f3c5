import React from 'react';
import { CardProps } from '@/types';

/**
 * Card component system with multiple variants following PerPixel design system
 * 
 * @param variant - Card style variant (default, testimonial, portfolio, contact, introduction)
 * @param theme - Card theme (light, dark)
 * @param children - Card content
 * @param className - Additional CSS classes
 * @param rounded - Apply rounded corners
 * @param padding - Padding size (sm, md, lg)
 */
const Card: React.FC<CardProps> = ({
  variant = 'default',
  theme = 'light',
  children,
  className = '',
  rounded = true,
  padding = 'md',
}) => {
  // Base card styles
  const baseStyles = `
    relative overflow-hidden
    transition-all duration-300
    ${rounded ? 'rounded-2xl' : ''}
  `;

  // Padding variants
  const paddingStyles = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
  };

  // Theme styles
  const themeStyles = {
    light: 'bg-white text-black',
    dark: 'bg-[#131d26] text-white',
  };

  // Variant-specific styles
  const variantStyles = {
    default: `
      ${themeStyles[theme]}
      border border-gray-200
      hover:shadow-lg hover:scale-[1.02]
    `,
    testimonial: `
      ${theme === 'light'
        ? 'bg-[#fadcd9] text-black'
        : 'bg-[#131d26] text-white'
      }
      hover:shadow-xl hover:scale-[1.02]
      ${rounded ? 'rounded-3xl' : ''}
    `,
    portfolio: `
      bg-[#feb172] text-white
      hover:shadow-xl hover:scale-[1.02]
      ${rounded ? 'rounded-[68px]' : ''}
    `,
    contact: `
      bg-[#f8afa6] text-black
      hover:shadow-lg hover:scale-[1.02]
      ${rounded ? 'rounded-2xl' : ''}
    `,
    introduction: `
      bg-[#fadcd9] text-black
      hover:shadow-lg hover:scale-[1.02]
      ${rounded ? 'rounded-[54px]' : ''}
    `,
  };

  // Combine all styles
  const cardClasses = `
    ${baseStyles}
    ${paddingStyles[padding]}
    ${variantStyles[variant]}
    ${className}
  `.replace(/\s+/g, ' ').trim();

  return (
    <div className={cardClasses}>
      {children}
    </div>
  );
};

// Compound components for flexible composition
const CardHeader: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className = '' 
}) => (
  <div className={`mb-4 ${className}`}>
    {children}
  </div>
);

const CardContent: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className = '' 
}) => (
  <div className={`flex-1 ${className}`}>
    {children}
  </div>
);

const CardFooter: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className = '' 
}) => (
  <div className={`mt-4 ${className}`}>
    {children}
  </div>
);

// Attach compound components to main Card component
Card.Header = CardHeader;
Card.Content = CardContent;
Card.Footer = CardFooter;

export default Card;

// Specialized card components for common use cases
export const TestimonialCard: React.FC<{
  testimonial: {
    name: string;
    company?: string;
    content: string;
    rating?: number;
    image?: string;
  };
  theme?: 'light' | 'dark';
  className?: string;
}> = ({ testimonial, theme = 'light', className = '' }) => (
  <Card variant="testimonial" theme={theme} className={className}>
    <Card.Header>
      {testimonial.rating && (
        <div className="flex items-center gap-1 mb-2">
          {[...Array(5)].map((_, i) => (
            <span 
              key={i} 
              className={`text-lg ${
                i < testimonial.rating! 
                  ? 'text-primary-orange' 
                  : 'text-gray-300'
              }`}
            >
              ★
            </span>
          ))}
        </div>
      )}
      <h3 className="text-xl font-semibold">
        "{testimonial.content.split(' ').slice(0, 4).join(' ')}..."
      </h3>
    </Card.Header>
    
    <Card.Content>
      <p className={`text-sm leading-relaxed ${
        theme === 'light' ? 'text-gray-700' : 'text-gray-300'
      }`}>
        {testimonial.content}
      </p>
    </Card.Content>
    
    <Card.Footer>
      <div className="flex items-center justify-between">
        <div>
          <p className="font-medium">{testimonial.name}</p>
          {testimonial.company && (
            <p className={`text-sm ${
              theme === 'light' ? 'text-gray-600' : 'text-gray-400'
            }`}>
              {testimonial.company}
            </p>
          )}
        </div>
        <button className={`text-sm font-medium ${
          theme === 'light' ? 'text-dark-bg' : 'text-text-light'
        } hover:underline`}>
          See full review →
        </button>
      </div>
    </Card.Footer>
  </Card>
);

export const PortfolioCard: React.FC<{
  project: {
    title: string;
    description?: string;
    image?: string;
    category?: string;
  };
  featured?: boolean;
  className?: string;
}> = ({ project, featured = false, className = '' }) => (
  <Card 
    variant={featured ? 'portfolio' : 'default'} 
    padding={featured ? 'lg' : 'md'}
    className={className}
  >
    {project.image && (
      <div className="mb-4 -mx-6 -mt-6">
        <img 
          src={project.image} 
          alt={project.title}
          className="w-full h-48 object-cover rounded-t-2xl"
        />
      </div>
    )}
    
    <Card.Header>
      {project.category && (
        <span className="text-xs font-medium uppercase tracking-wide opacity-75 mb-2 block">
          {project.category}
        </span>
      )}
      <h3 className={`font-bold ${featured ? 'text-2xl' : 'text-lg'}`}>
        {project.title}
      </h3>
    </Card.Header>
    
    {project.description && (
      <Card.Content>
        <p className={`leading-relaxed ${
          featured ? 'text-text-light/90' : 'text-gray-700'
        }`}>
          {project.description}
        </p>
      </Card.Content>
    )}
  </Card>
);

export const ContactCard: React.FC<{
  title: string;
  description?: string;
  icon?: React.ReactNode;
  action?: {
    label: string;
    onClick: () => void;
  };
  className?: string;
}> = ({ title, description, icon, action, className = '' }) => (
  <Card variant="contact" className={className}>
    <Card.Header>
      <div className="flex items-start justify-between">
        <h3 className="text-lg font-medium">{title}</h3>
        {icon && (
          <div className="text-2xl opacity-75">
            {icon}
          </div>
        )}
      </div>
    </Card.Header>
    
    {description && (
      <Card.Content>
        <p className="text-gray-700 leading-relaxed">
          {description}
        </p>
      </Card.Content>
    )}
    
    {action && (
      <Card.Footer>
        <button 
          onClick={action.onClick}
          className="text-dark-bg font-medium hover:underline"
        >
          {action.label}
        </button>
      </Card.Footer>
    )}
  </Card>
);
