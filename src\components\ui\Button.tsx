import React from 'react';
import Link from 'next/link';
import { ButtonProps } from '@/types';

/**
 * Button component with multiple variants following PerPixel design system
 * 
 * @param variant - Button style variant (primary, secondary, cta, link)
 * @param size - Button size (sm, md, lg)
 * @param children - Button content
 * @param onClick - Click handler for button actions
 * @param href - URL for link buttons
 * @param disabled - Disabled state
 * @param className - Additional CSS classes
 * @param icon - Icon element to display
 * @param iconPosition - Position of icon (left, right)
 */
const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  children,
  onClick,
  href,
  disabled = false,
  className = '',
  icon,
  iconPosition = 'right',
}) => {
  // Base button styles
  const baseStyles = `
    inline-flex items-center justify-center
    font-semibold transition-all duration-200
    focus:outline-none focus:ring-2 focus:ring-offset-2
    disabled:opacity-50 disabled:cursor-not-allowed
    whitespace-nowrap
  `;

  // Size variants
  const sizeStyles = {
    sm: 'px-4 py-2 text-sm rounded-lg gap-2',
    md: 'px-6 py-3 text-base rounded-xl gap-3',
    lg: 'px-10 py-4 text-xl rounded-full gap-3',
  };

  // Variant styles based on PerPixel design system
  const variantStyles = {
    primary: `
      bg-[#131d26] text-white
      hover:bg-[#1A1E29] hover:scale-105
      focus:ring-[#131d26]
      shadow-md hover:shadow-lg
    `,
    secondary: `
      bg-white text-[#131d26] border-2 border-transparent
      hover:bg-[#feb172] hover:text-white hover:scale-105
      focus:ring-[#feb172]
      shadow-md hover:shadow-lg
    `,
    cta: `
      bg-[#feb172] text-white
      hover:bg-[#feb172]/90 hover:scale-105
      focus:ring-[#feb172]
      shadow-lg hover:shadow-xl
    `,
    link: `
      bg-transparent text-[#131d26] underline-offset-4
      hover:underline hover:text-[#feb172]
      focus:ring-[#feb172]
      px-0 py-0
    `,
  };

  // Combine all styles
  const buttonClasses = `
    ${baseStyles}
    ${sizeStyles[size]}
    ${variantStyles[variant]}
    ${className}
  `.replace(/\s+/g, ' ').trim();

  // Icon rendering
  const renderIcon = () => {
    if (!icon) return null;
    return <span className="flex-shrink-0">{icon}</span>;
  };

  // Content with icon positioning
  const renderContent = () => {
    if (!icon) return children;
    
    return (
      <>
        {iconPosition === 'left' && renderIcon()}
        <span>{children}</span>
        {iconPosition === 'right' && renderIcon()}
      </>
    );
  };

  // If href is provided, render as Link
  if (href) {
    const isExternal = href.startsWith('http') || href.startsWith('mailto') || href.startsWith('tel');
    
    if (isExternal) {
      return (
        <a
          href={href}
          className={buttonClasses}
          target="_blank"
          rel="noopener noreferrer"
          aria-disabled={disabled}
        >
          {renderContent()}
        </a>
      );
    }
    
    return (
      <Link href={href} className={buttonClasses} aria-disabled={disabled}>
        {renderContent()}
      </Link>
    );
  }

  // Regular button
  return (
    <button
      type="button"
      className={buttonClasses}
      onClick={onClick}
      disabled={disabled}
    >
      {renderContent()}
    </button>
  );
};

export default Button;

// Export common button configurations for easy reuse
export const PrimaryButton: React.FC<Omit<ButtonProps, 'variant'>> = (props) => (
  <Button variant="primary" {...props} />
);

export const SecondaryButton: React.FC<Omit<ButtonProps, 'variant'>> = (props) => (
  <Button variant="secondary" {...props} />
);

export const CTAButton: React.FC<Omit<ButtonProps, 'variant'>> = (props) => (
  <Button variant="cta" {...props} />
);

export const LinkButton: React.FC<Omit<ButtonProps, 'variant'>> = (props) => (
  <Button variant="link" {...props} />
);

// Portfolio button with arrow icon (matching existing design)
export const PortfolioButton: React.FC<Omit<ButtonProps, 'variant' | 'icon'>> = (props) => (
  <Button 
    variant="primary" 
    icon={<span className="ml-1">↗</span>}
    iconPosition="right"
    {...props}
  />
);

// Hire me button (matching existing design)
export const HireMeButton: React.FC<Omit<ButtonProps, 'variant'>> = (props) => (
  <Button variant="secondary" {...props} />
);
