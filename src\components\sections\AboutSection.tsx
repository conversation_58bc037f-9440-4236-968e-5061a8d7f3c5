import React from 'react';
import { AboutSectionProps } from '../../types';
import { SectionContainer } from '../layout/Container';
import {
  SectionHeader,
  Heading,
  BodyText
} from '../ui/Typography';
import Card, { ContactCard } from '../ui/Card';
import Image from '../ui/Image';

/**
 * About section component based on Figma design
 * Features introduction card, contact section, and placeholder cards
 */
const AboutSection: React.FC<AboutSectionProps> = ({
  title = "About Us",
  introduction = {
    title: "Introduction",
    content: "<PERSON> is an innovative AI artist, renowned for blending cutting-edge technology with creative expression. Based in LA, she crafts unique digital art experiences accessible globally."
  },
  contact = {
    title: "Get in Touch",
    methods: [
      { type: "Email", value: "<EMAIL>", icon: "📧" },
      { type: "Phone", value: "+****************", icon: "📞" },
      { type: "Location", value: "Los Angeles, CA", icon: "📍" }
    ]
  },
  className = '',
}) => {
  return (
    <SectionContainer background="default" className={className}>
      {/* Section Header with decorative underline */}
      <div className="text-center mb-16">
        <div className="relative inline-block">
          <SectionHeader color="dark" className="mb-4">
            {title}
          </SectionHeader>
          {/* Decorative underline */}
          <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-24 h-0.5 bg-primary-orange"></div>
        </div>
      </div>

      {/* Main content grid */}
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 mb-16">
        {/* Left placeholder card */}
        <div className="lg:col-span-3">
          <Card 
            variant="introduction" 
            padding="lg"
            className="h-full min-h-[400px] lg:min-h-[670px]"
          >
            {/* Placeholder content - can be replaced with actual content */}
            <div className="flex items-center justify-center h-full">
              <div className="text-center opacity-50">
                <div className="w-16 h-16 bg-primary-orange/20 rounded-full mx-auto mb-4"></div>
                <BodyText color="muted">Content placeholder</BodyText>
              </div>
            </div>
          </Card>
        </div>

        {/* Right content area */}
        <div className="lg:col-span-9 space-y-8">
          {/* Introduction section */}
          <div>
            <Heading color="dark" className="mb-6">
              {introduction.title}
            </Heading>
            
            {/* Introduction card */}
            <Card 
              variant="introduction" 
              padding="lg"
              className="mb-8"
            >
              <div className="flex items-start gap-6">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-primary-orange/20 rounded-full flex items-center justify-center">
                    <span className="text-2xl">✨</span>
                  </div>
                </div>
                <div className="flex-1">
                  <BodyText className="leading-relaxed">
                    {introduction.content}
                  </BodyText>
                </div>
              </div>
            </Card>
          </div>

          {/* Decorative background line */}
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-200"></div>
            </div>
          </div>

          {/* Get in Touch section */}
          <div>
            <div className="flex items-center justify-between mb-6">
              <Heading color="dark">
                {contact.title}
              </Heading>
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-primary-orange/20 rounded-full flex items-center justify-center">
                  <span className="text-2xl">📞</span>
                </div>
              </div>
            </div>

            {/* Contact methods grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {contact.methods.map((method, index) => (
                <ContactCard
                  key={index}
                  title={method.type}
                  description={method.value}
                  icon={<span className="text-2xl">{method.icon}</span>}
                  action={{
                    label: "Contact",
                    onClick: () => {
                      if (method.type === "Email") {
                        window.open(`mailto:${method.value}`);
                      } else if (method.type === "Phone") {
                        window.open(`tel:${method.value}`);
                      }
                    }
                  }}
                  className="h-full"
                />
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Bottom decorative line */}
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-gray-200"></div>
        </div>
      </div>
    </SectionContainer>
  );
};

export default AboutSection;

// Specialized About section variants
export const MinimalAboutSection: React.FC<{
  title?: string;
  description: string;
  className?: string;
}> = ({ title = "About Us", description, className = '' }) => (
  <SectionContainer background="secondary" className={className}>
    <div className="text-center max-w-4xl mx-auto">
      <SectionHeader color="dark" className="mb-8">
        {title}
      </SectionHeader>
      <BodyText size="lg" className="leading-relaxed">
        {description}
      </BodyText>
    </div>
  </SectionContainer>
);

export const FeatureAboutSection: React.FC<{
  features: Array<{
    title: string;
    description: string;
    icon: React.ReactNode;
  }>;
  className?: string;
}> = ({ features, className = '' }) => (
  <SectionContainer background="default" className={className}>
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {features.map((feature, index) => (
        <Card key={index} variant="default" padding="lg" className="text-center">
          <div className="mb-4 flex justify-center">
            <div className="w-16 h-16 bg-primary-orange/10 rounded-full flex items-center justify-center">
              {feature.icon}
            </div>
          </div>
          <Heading className="mb-4 text-lg">
            {feature.title}
          </Heading>
          <BodyText color="muted">
            {feature.description}
          </BodyText>
        </Card>
      ))}
    </div>
  </SectionContainer>
);
