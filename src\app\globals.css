@import "tailwindcss";

/* Custom Keyframes for HeroShapes */
@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}
@keyframes float-reverse {
  0% { transform: translateY(0px); }
  50% { transform: translateY(20px); }
  100% { transform: translateY(0px); }
}
@keyframes float-delayed {
  0% { transform: translateY(0px); }
  25% { transform: translateY(-10px); }
  50% { transform: translateY(-25px); }
  75% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}
@keyframes spin-slow {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
@keyframes spin-reverse {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(-360deg); }
}
@keyframes bounce-slow {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

/* Animation Utilities */
.animate-float {
  animation: float 4s ease-in-out infinite;
}
.animate-float-reverse {
  animation: float-reverse 4s ease-in-out infinite;
}
.animate-float-delayed {
  animation: float-delayed 6s ease-in-out infinite;
}
.animate-spin-slow {
  animation: spin-slow 10s linear infinite;
}
.animate-spin-reverse {
  animation: spin-reverse 10s linear infinite;
}
.animate-bounce-slow {
  animation: bounce-slow 3s ease-in-out infinite;
}


:root {
  --background: #ffffff;
  --foreground: #171717;

  /* PerPixel Design System Colors */
  --primary-orange: #feb172;
  --secondary-peach: #fadcd9;
  --accent-pink: #f8afa6;
  --dark-bg: #131d26;
  --text-dark: #000000;
  --text-light: #ffffff;
  --gray-placeholder: #d9d9d9;
  --text-muted: #344053;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Custom PerPixel Colors */
  --color-primary-orange: var(--primary-orange);
  --color-secondary-peach: var(--secondary-peach);
  --color-accent-pink: var(--accent-pink);
  --color-dark-bg: var(--dark-bg);
  --color-text-dark: var(--text-dark);
  --color-text-light: var(--text-light);
  --color-gray-placeholder: var(--gray-placeholder);
  --color-text-muted: var(--text-muted);

  /* Custom PerPixel Fonts */
  --font-inter: var(--font-inter);
  --font-playfair: var(--font-playfair);

  /* Custom Font Sizes for Large Display Text */
  --font-size-display-xl: 15rem;     /* 240px - for "PerPixel" */
  --font-size-display-lg: 12rem;     /* 192px - for "Agency" */
  --font-size-display-md: 19.375rem; /* 310px - for "Creative" */
  --font-size-display-sm: 15.625rem; /* 250px - for "Design" */
  --font-size-hero: 7.5rem;          /* 120px - for "PORTFOLIO" */
  --font-size-section: 5.063rem;     /* 81px - for "Testimonials" */
  --font-size-large: 4rem;           /* 64px - for "2025" */
  --font-size-heading: 3rem;         /* 48px - for section headings */
  --font-size-subheading: 2.75rem;   /* 44px - for testimonial titles */
  --font-size-body-lg: 1.375rem;     /* 22px - for body text */
  --font-size-body: 1rem;            /* 16px - for regular text */
  --font-size-caption: 0.9375rem;    /* 15px - for captions */

  /* Button Sizes */
  --button-size-sm: 0.875rem;        /* 14px */
  --button-size-md: 1rem;            /* 16px */
  --button-size-lg: 1.5rem;          /* 24px */
  --button-size-xl: 2rem;            /* 32px */

  /* Custom Responsive Breakpoints */
  --breakpoint-xs: 480px;   /* Extra small devices */
  --breakpoint-sm: 640px;   /* Small devices */
  --breakpoint-md: 768px;   /* Medium devices */
  --breakpoint-lg: 1024px;  /* Large devices */
  --breakpoint-xl: 1280px;  /* Extra large devices */
  --breakpoint-2xl: 1440px; /* Design target width */
  --breakpoint-3xl: 1600px; /* Ultra wide */

  /* Container Max Widths */
  --container-sm: 100%;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1440px;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

html, body {
  overflow-x: hidden;
  scrollbar-width: none; /* Firefox */
}
body::-webkit-scrollbar {
  display: none;
}
body {
  -ms-overflow-style: none; /* IE and Edge */
}
body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}
