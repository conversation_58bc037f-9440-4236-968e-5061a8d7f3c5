"use client";
import React from "react";
import Image from "next/image";

// TODO: Replace these srcs with real assets later
const sampleImage = "/img/sample.jpg";
const museaImage = "/img/musea.jpg";

const PortfolioGrid = () => (
  <section className="w-full min-h-[80vh] bg-[#FFF8F6] flex flex-col items-center justify-center py-24 px-4">
    <div className="max-w-[1400px] w-full grid grid-cols-4 grid-rows-3 gap-8">
      {/* Left large card */}
      <div className="bg-[#FFD9D2] rounded-2xl p-8 flex flex-col justify-between row-span-2">
        <div>
          <h2 className="text-3xl font-bold mb-2">Artist Redefining <span className="italic font-normal">Architecture</span> <b>with</b> <br/>Al-Driven Design</h2>
        </div>
        <div className="mt-4 flex justify-end">
          <span className="w-16 h-16 bg-[#F8B7B7] rounded-full inline-block" />
        </div>
      </div>

      {/* Center image card */}
      <div className="bg-[#F7D6D2] rounded-2xl flex items-center justify-center row-span-2">
        <Image src={sampleImage} alt="Profile" width={300} height={300} className="rounded-2xl object-cover w-full h-full" />
      </div>

      {/* Right project card */}
      <div className="bg-[#FFD9D2] rounded-2xl p-4 flex flex-col row-span-2">
        <div className="flex-1 flex flex-col gap-2">
          <div className="w-full h-32 rounded-xl bg-[#F8B7B7] mb-2 overflow-hidden">
            <Image src={museaImage} alt="Musea" width={200} height={100} className="object-cover w-full h-full" />
          </div>
          <div className="flex justify-between items-center mb-2">
            <span className="text-lg font-semibold">Musea</span>
            <span className="text-lg">↗</span>
          </div>
          <div className="text-base">Elara</div>
          <div className="text-base">Verve</div>
          <div className="text-base">Zephyr</div>
        </div>
      </div>

      {/* Bottom left info card */}
      <div className="bg-[#FFE8E1] rounded-2xl p-6 flex flex-col justify-between">
        <div className="text-3xl font-extrabold tracking-tight mb-2">PER PIXEL</div>
        <div className="text-base font-medium text-[#23283B]">Julia Huang is an innovative AI artist, renowned for blending cutting-edge technology with creative expression. Based in LA, she crafts unique digital art experiences accessible globally.</div>
      </div>

      {/* Bottom center contact card */}
      <div className="bg-[#FFD9D2] rounded-2xl flex flex-col justify-between items-start p-6">
        <div className="text-xs text-[#23283B] mb-1">Have some questions?</div>
        <div className="text-3xl font-bold">Contact me</div>
      </div>

      {/* Bottom right social card */}
      <div className="bg-[#FFE8E1] rounded-2xl flex flex-row items-center justify-around p-4 gap-2">
        <span className="text-xs font-medium">INSTAGRAM</span>
        <span className="text-xs font-medium">TWITTER</span>
        <span className="text-xs font-medium">LINKEDIN</span>
      </div>
    </div>
  </section>
);

export default PortfolioGrid;
