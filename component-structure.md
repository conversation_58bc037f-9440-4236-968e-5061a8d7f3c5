# Component Structure - PerPixel Project

## Directory Organization

```
src/components/
├── layout/                 # Layout and navigation components
│   ├── Header.tsx         # Main site header
│   ├── Footer.tsx         # Main site footer
│   ├── Navigation.tsx     # Navigation component
│   └── Container.tsx      # Responsive container wrapper
├── sections/              # Page section components
│   ├── HeroSection.tsx    # New integrated hero section
│   ├── AboutSection.tsx   # About us section
│   ├── PortfolioSection.tsx # Portfolio showcase section
│   ├── TestimonialsSection.tsx # Testimonials section
│   └── ContactSection.tsx # Contact section
├── ui/                    # Reusable UI components
│   ├── Button.tsx         # Button variants (primary, secondary, CTA)
│   ├── Card.tsx           # Card component system
│   ├── Typography.tsx     # Typography components
│   ├── SocialLinks.tsx    # Social media links
│   └── Image.tsx          # Optimized image wrapper
├── Hero Section/          # PRESERVE EXISTING - Legacy hero components
│   ├── Hero.tsx          # Current hero implementation
│   ├── HeroShapes.tsx    # CRITICAL - Animated shapes (DO NOT MODIFY)
│   └── PortfolioGrid.tsx # Current portfolio grid
├── buttons/               # PRESERVE EXISTING - Legacy button components
│   └── GetInTouch.tsx    # Existing button component
└── Navbar.tsx            # PRESERVE EXISTING - Current navbar
```

## Component Naming Conventions

### File Naming
- **PascalCase** for component files: `HeroSection.tsx`
- **Descriptive names** that indicate purpose: `TestimonialsSection.tsx`
- **Consistent suffixes**: 
  - `Section.tsx` for page sections
  - `Component.tsx` for complex components
  - No suffix for simple UI components

### Component Naming
- **PascalCase** for component names: `const HeroSection = () => {}`
- **Descriptive and specific**: `TestimonialsCard` not `Card`
- **Consistent with file names**: File `Button.tsx` exports `Button`

### Props Interface Naming
- **Component name + Props**: `ButtonProps`, `HeroSectionProps`
- **Descriptive for complex props**: `TestimonialCardData`

## Component Categories

### 1. Layout Components (`/layout`)
**Purpose**: Handle page structure, navigation, and responsive containers

- **Header.tsx**: Site header with logo and navigation
- **Footer.tsx**: Site footer with links and social media
- **Navigation.tsx**: Main navigation component
- **Container.tsx**: Responsive container with max-width handling

### 2. Section Components (`/sections`)
**Purpose**: Large page sections that compose the homepage

- **HeroSection.tsx**: New integrated hero section
- **AboutSection.tsx**: About us content section
- **PortfolioSection.tsx**: Portfolio showcase and project grid
- **TestimonialsSection.tsx**: Customer testimonials
- **ContactSection.tsx**: Contact information and forms

### 3. UI Components (`/ui`)
**Purpose**: Reusable, composable UI elements

- **Button.tsx**: All button variants with consistent styling
- **Card.tsx**: Card component system with multiple variants
- **Typography.tsx**: Text components with design system integration
- **SocialLinks.tsx**: Social media link components
- **Image.tsx**: Next.js Image wrapper with optimization

### 4. Legacy Components (PRESERVE)
**Purpose**: Existing components that must be maintained

- **Hero Section/**: Complete existing hero implementation
- **buttons/**: Existing button components
- **Navbar.tsx**: Current navigation implementation

## Component Composition Strategy

### Hierarchical Composition
```tsx
// Page level composition
<Container>
  <Header />
  <main>
    <HeroSection />
    <AboutSection />
    <PortfolioSection />
    <TestimonialsSection />
    <ContactSection />
  </main>
  <Footer />
</Container>
```

### Section Level Composition
```tsx
// Section composed of UI components
<section className="py-16">
  <Container>
    <Typography variant="section-header">About Us</Typography>
    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
      <Card variant="introduction">
        <Typography variant="heading">Introduction</Typography>
        <Typography variant="body">Content...</Typography>
      </Card>
      <Card variant="contact">
        <Typography variant="heading">Get in Touch</Typography>
        <Button variant="primary">Contact Us</Button>
      </Card>
    </div>
  </Container>
</section>
```

### UI Level Composition
```tsx
// UI components with flexible composition
<Card variant="testimonial" theme="dark">
  <Card.Header>
    <Typography variant="testimonial-title">Amazing Team</Typography>
  </Card.Header>
  <Card.Content>
    <Typography variant="body">Lorem ipsum...</Typography>
  </Card.Content>
  <Card.Footer>
    <Button variant="link">See full review</Button>
  </Card.Footer>
</Card>
```

## Data Flow Strategy

### Props Down, Events Up
- **Data flows down** through props
- **Events bubble up** through callbacks
- **State management** at appropriate component level

### State Management Levels
1. **Component State**: Local UI state (hover, focus, etc.)
2. **Section State**: Section-specific state (form data, etc.)
3. **Page State**: Cross-section state (navigation, theme, etc.)
4. **Global State**: App-wide state (user preferences, etc.)

## TypeScript Integration

### Interface Definitions
```tsx
// Component props interface
interface HeroSectionProps {
  title: string;
  subtitle: string;
  ctaButtons: ButtonProps[];
  backgroundImage?: string;
}

// Data interfaces
interface TestimonialData {
  id: string;
  name: string;
  company: string;
  content: string;
  rating: number;
  image?: string;
}

// Variant types
type ButtonVariant = 'primary' | 'secondary' | 'cta' | 'link';
type CardVariant = 'default' | 'testimonial' | 'portfolio' | 'contact';
```

### Component Type Patterns
```tsx
// Functional component with props
const HeroSection: React.FC<HeroSectionProps> = ({ title, subtitle }) => {
  return <section>...</section>;
};

// Component with children
interface CardProps {
  variant?: CardVariant;
  children: React.ReactNode;
  className?: string;
}

// Component with render props
interface PortfolioGridProps {
  items: PortfolioItem[];
  renderItem: (item: PortfolioItem) => React.ReactNode;
}
```

## Integration Guidelines

### Existing Component Integration
1. **Preserve HeroShapes.tsx** completely
2. **Wrap existing components** rather than modify
3. **Gradual migration** from old to new components
4. **Maintain backward compatibility**

### New Component Development
1. **Start with UI components** (Button, Card, Typography)
2. **Build sections** using UI components
3. **Integrate sections** into page layout
4. **Test integration** with existing components

### Performance Considerations
1. **Lazy loading** for below-the-fold sections
2. **Code splitting** for large components
3. **Memoization** for expensive computations
4. **Image optimization** through Next.js Image

## Testing Strategy

### Component Testing
- **Unit tests** for individual components
- **Integration tests** for component composition
- **Visual regression tests** for design consistency
- **Accessibility tests** for WCAG compliance

### Testing Structure
```
src/components/
├── __tests__/
│   ├── ui/
│   │   ├── Button.test.tsx
│   │   └── Card.test.tsx
│   ├── sections/
│   │   └── HeroSection.test.tsx
│   └── layout/
│       └── Header.test.tsx
```

## Documentation Requirements

### Component Documentation
- **JSDoc comments** for all components
- **Props documentation** with examples
- **Usage examples** in Storybook or similar
- **Design system integration** notes

### Example Documentation
```tsx
/**
 * Primary button component with multiple variants
 * 
 * @param variant - Button style variant
 * @param size - Button size (sm, md, lg)
 * @param children - Button content
 * @param onClick - Click handler
 * 
 * @example
 * <Button variant="primary" size="lg" onClick={handleClick}>
 *   Get Started
 * </Button>
 */
interface ButtonProps {
  variant: 'primary' | 'secondary' | 'cta';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  onClick?: () => void;
}
```
