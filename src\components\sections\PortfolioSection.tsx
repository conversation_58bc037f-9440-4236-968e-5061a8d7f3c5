import React from 'react';
import { PortfolioSectionProps } from '../../types';
import { SectionContainer } from '../layout/Container';
import {
  PortfolioTitle,
  YearDisplay,
  ProjectTitle,
  BodyText,
  Caption
} from '../ui/Typography';
import Card, { PortfolioCard } from '../ui/Card';
import Button from '../ui/Button';
import { ContactSocialLinks } from '../ui/SocialLinks';

/**
 * Portfolio section component based on Figma design
 * Features large portfolio showcase, project grid, and quick links
 */
const PortfolioSection: React.FC<PortfolioSectionProps> = ({
  title,
  featured = {
    id: 'featured-2025',
    title: 'Artist Redefining Architecture with AI-Driven Design',
    description: 'Innovative AI-driven architectural designs that blend technology with creative expression.',
    category: 'AI Architecture',
    featured: true
  },
  projects = [
    { id: '1', title: 'Musea', category: 'Architecture' },
    { id: '2', title: 'Elara', category: 'Design' },
    { id: '3', title: 'Verve', category: 'Branding' },
    { id: '4', title: 'Zephyr', category: 'Digital Art' }
  ],
  quickLinks = [
    { title: 'BLOG', href: '/blog', icon: '📝' },
    { title: 'WORK', href: '/work', icon: '💼' },
    { title: 'ABOUT', href: '/about', icon: '👋' },
    { title: 'CONTACT', href: '/contact', icon: '📞' }
  ],
  className = '',
}) => {
  const socialLinks = [
    { platform: 'instagram' as const, url: 'https://instagram.com/perpixel' },
    { platform: 'twitter' as const, url: 'https://twitter.com/perpixel' },
    { platform: 'linkedin' as const, url: 'https://linkedin.com/company/perpixel' }
  ];

  return (
    <SectionContainer background="default" className={className}>
      {/* Main portfolio showcase */}
      <div className="relative mb-24">
        {/* Large portfolio card with year display */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 items-center">
          {/* Left side - Featured project */}
          <div className="lg:col-span-7">
            <Card 
              variant="portfolio" 
              padding="lg"
              className="min-h-[500px] lg:min-h-[830px] relative overflow-hidden"
            >
              {/* Social links at top */}
              <div className="absolute top-8 left-8 right-8 z-10">
                <ContactSocialLinks links={socialLinks} />
              </div>

              {/* Main content */}
              <div className="absolute bottom-8 left-8 right-8 z-10">
                <div className="mb-8">
                  <BodyText size="lg" color="light" className="leading-relaxed">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem
                    ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum
                    dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit
                    amet, consectetur adipiscing elit.
                  </BodyText>
                </div>
              </div>

              {/* Year display */}
              <div className="absolute bottom-8 left-8 z-10">
                <YearDisplay year="2025" />
              </div>
            </Card>
          </div>

          {/* Right side - Portfolio title and project list */}
          <div className="lg:col-span-5 space-y-8">
            {/* Large PORTFOLIO text */}
            <div className="text-center lg:text-left">
              <PortfolioTitle>PORTFOLIO</PortfolioTitle>
            </div>

            {/* Project list */}
            <Card variant="introduction" padding="lg" className="min-h-[400px]">
              <div className="space-y-8">
                {/* Featured project with image */}
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <ProjectTitle>{projects[0]?.title || 'Musea'}</ProjectTitle>
                    <div className="w-6 h-6 bg-primary-orange/20 rounded-full flex items-center justify-center">
                      <span className="text-xs">→</span>
                    </div>
                  </div>
                  
                  {/* Project image placeholder */}
                  <div className="w-full h-48 bg-gray-200 rounded-2xl overflow-hidden">
                    <div className="w-full h-full bg-gradient-to-br from-primary-orange/20 to-secondary-peach/40 flex items-center justify-center">
                      <span className="text-gray-500">Project Image</span>
                    </div>
                  </div>
                  
                  {/* Divider */}
                  <div className="w-full h-0.5 bg-gray-200"></div>
                </div>

                {/* Other projects */}
                {projects.slice(1).map((project, index) => (
                  <div key={project.id} className="space-y-4">
                    <div className="flex items-center justify-between">
                      <ProjectTitle>{project.title}</ProjectTitle>
                    </div>
                    {index < projects.length - 2 && (
                      <div className="w-full h-0.5 bg-gray-200"></div>
                    )}
                  </div>
                ))}
              </div>
            </Card>
          </div>
        </div>
      </div>

      {/* Quick Links section */}
      <div className="mb-16">
        {/* Background decorative line */}
        <div className="relative mb-8">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t-2 border-gray-200"></div>
          </div>
        </div>

        {/* Quick Links header */}
        <div className="mb-8">
          <ProjectTitle>QUICK LINKS</ProjectTitle>
        </div>

        {/* Quick links grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {quickLinks.map((link, index) => (
            <Card 
              key={index}
              variant="default" 
              padding="md"
              className="border-4 border-black hover:bg-secondary-peach transition-colors cursor-pointer group"
            >
              <div className="flex items-center justify-between">
                <ProjectTitle className="group-hover:text-primary-orange transition-colors">
                  {link.title}
                </ProjectTitle>
                <div className="text-2xl group-hover:scale-110 transition-transform">
                  {link.icon}
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Bottom decorative line */}
        <div className="relative mt-8">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t-2 border-gray-200"></div>
          </div>
        </div>
      </div>

      {/* Featured project showcase */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left - Featured project card */}
        <div className="lg:col-span-1">
          <PortfolioCard
            project={featured}
            featured={true}
            className="h-full"
          />
        </div>

        {/* Center - Image placeholder */}
        <div className="lg:col-span-1">
          <div className="h-full min-h-[400px] bg-gradient-to-br from-secondary-peach to-primary-orange/30 rounded-2xl flex items-center justify-center">
            <span className="text-gray-600">Featured Image</span>
          </div>
        </div>

        {/* Right - Contact and branding */}
        <div className="lg:col-span-1 space-y-6">
          {/* Contact card */}
          <Card variant="contact" padding="lg" className="relative overflow-hidden">
            <div className="flex items-start justify-between mb-8">
              <BodyText className="text-sm leading-relaxed">
                Have some<br />questions?
              </BodyText>
              <div className="text-2xl">❓</div>
            </div>
            <div className="absolute bottom-6 left-6">
              <ProjectTitle className="text-3xl">Contact me</ProjectTitle>
            </div>
          </Card>

          {/* About card */}
          <Card variant="introduction" padding="lg">
            <div className="space-y-4">
              <div className="text-2xl">💡</div>
              <BodyText className="text-sm leading-relaxed">
                Julia Huang is an innovative AI artist, renowned for blending
                cutting-edge technology with creative expression. Based in LA, she
                crafts unique digital art experiences accessible globally.
              </BodyText>
            </div>
          </Card>

          {/* Social links card */}
          <ContactSocialLinks links={socialLinks} />
        </div>
      </div>

      {/* PerPixel branding */}
      <div className="mt-16 text-center">
        <ProjectTitle className="text-5xl lg:text-6xl tracking-wider">
          PER PIXEL
        </ProjectTitle>
      </div>
    </SectionContainer>
  );
};

export default PortfolioSection;
