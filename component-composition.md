# Component Composition Strategy - PerPixel Project

## Overview
This document defines how components will compose together, data flow patterns, and integration strategies for the PerPixel project.

## Composition Architecture

### 1. Hierarchical Component Structure

```
Page Level (app/page.tsx)
├── Layout Components
│   ├── Header
│   └── Footer
└── Main Content
    ├── HeroSection (integrated with existing HeroShapes)
    ├── AboutSection
    ├── PortfolioSection
    ├── TestimonialsSection
    └── ContactSection
```

### 2. Component Composition Patterns

#### Container-Presenter Pattern
```tsx
// Container handles logic and state
const TestimonialsContainer = () => {
  const [testimonials, setTestimonials] = useState<TestimonialData[]>([]);
  
  return (
    <TestimonialsSection 
      testimonials={testimonials}
      onLoadMore={handleLoadMore}
    />
  );
};

// Presenter handles display
const TestimonialsSection = ({ testimonials, onLoadMore }) => {
  return (
    <section>
      {testimonials.map(testimonial => (
        <TestimonialCard key={testimonial.id} testimonial={testimonial} />
      ))}
    </section>
  );
};
```

#### Compound Component Pattern
```tsx
// Card with flexible composition
<Card variant="testimonial" theme="dark">
  <Card.Header>
    <Typography variant="subheading">Amazing Team</Typography>
  </Card.Header>
  <Card.Content>
    <Typography variant="body">Lorem ipsum...</Typography>
  </Card.Content>
  <Card.Footer>
    <Button variant="link">See full review</Button>
  </Card.Footer>
</Card>
```

#### Render Props Pattern
```tsx
// Flexible portfolio grid
<PortfolioGrid 
  items={portfolioItems}
  renderItem={(item) => (
    <PortfolioCard 
      item={item} 
      variant="featured"
      onClick={handleItemClick}
    />
  )}
/>
```

## Data Flow Strategy

### 1. Props Down, Events Up

```tsx
// Parent component manages state
const HomePage = () => {
  const [selectedProject, setSelectedProject] = useState<string | null>(null);
  
  return (
    <main>
      <PortfolioSection 
        projects={projects}
        selectedProject={selectedProject}
        onProjectSelect={setSelectedProject}
      />
    </main>
  );
};

// Child component receives props and emits events
const PortfolioSection = ({ projects, selectedProject, onProjectSelect }) => {
  return (
    <section>
      {projects.map(project => (
        <PortfolioCard
          key={project.id}
          item={project}
          selected={selectedProject === project.id}
          onClick={() => onProjectSelect(project.id)}
        />
      ))}
    </section>
  );
};
```

### 2. Context for Shared State

```tsx
// Theme context for design system
const ThemeContext = createContext<ThemeProps>({
  colors: {
    primary: '#feb172',
    secondary: '#fadcd9',
    accent: '#f8afa6',
  }
});

// Usage in components
const Button = ({ variant, children }) => {
  const theme = useContext(ThemeContext);
  const bgColor = variant === 'primary' ? theme.colors.primary : theme.colors.secondary;
  
  return (
    <button style={{ backgroundColor: bgColor }}>
      {children}
    </button>
  );
};
```

### 3. Custom Hooks for Logic

```tsx
// Custom hook for testimonials
const useTestimonials = () => {
  const [testimonials, setTestimonials] = useState<TestimonialData[]>([]);
  const [loading, setLoading] = useState(false);
  
  const loadTestimonials = useCallback(async () => {
    setLoading(true);
    // Load testimonials logic
    setLoading(false);
  }, []);
  
  return { testimonials, loading, loadTestimonials };
};

// Usage in component
const TestimonialsSection = () => {
  const { testimonials, loading, loadTestimonials } = useTestimonials();
  
  return (
    <section>
      {/* Render testimonials */}
    </section>
  );
};
```

## Integration with Existing Components

### 1. HeroShapes Integration Strategy

```tsx
// Preserve existing HeroShapes component
import HeroShapes from '../Hero Section/HeroShapes';

// New HeroSection wraps and integrates with HeroShapes
const HeroSection = ({ title, subtitle, ctaButtons }) => {
  return (
    <section className="relative">
      {/* Preserve existing HeroShapes */}
      <HeroShapes />
      
      {/* New content integrated around shapes */}
      <div className="relative z-10">
        <Typography variant="display-xl">{title}</Typography>
        <Typography variant="display-lg">{subtitle}</Typography>
        
        <div className="flex gap-4">
          {ctaButtons?.map((button, index) => (
            <Button key={index} {...button} />
          ))}
        </div>
      </div>
    </section>
  );
};
```

### 2. Gradual Migration Strategy

```tsx
// Phase 1: Wrapper approach
const LegacyHeroWrapper = () => {
  return (
    <div className="legacy-hero-wrapper">
      {/* Keep existing Hero component */}
      <LegacyHero />
      
      {/* Add new sections below */}
      <AboutSection />
      <PortfolioSection />
    </div>
  );
};

// Phase 2: Selective replacement
const HybridHero = () => {
  return (
    <section className="hero-section">
      {/* Keep HeroShapes */}
      <HeroShapes />
      
      {/* Replace other parts with new components */}
      <div className="hero-content">
        <Typography variant="display-xl">PerPixel</Typography>
        <Typography variant="display-lg">Agency</Typography>
        <div className="cta-buttons">
          <Button variant="primary">Portfolio</Button>
          <Button variant="secondary">Hire me</Button>
        </div>
      </div>
    </section>
  );
};

// Phase 3: Full integration
const NewHeroSection = () => {
  return (
    <section className="integrated-hero">
      <HeroShapes />
      {/* Fully integrated new design */}
    </section>
  );
};
```

## Component Communication Patterns

### 1. Sibling Communication via Parent

```tsx
const HomePage = () => {
  const [activeSection, setActiveSection] = useState('hero');
  
  return (
    <main>
      <Navigation 
        activeSection={activeSection}
        onSectionChange={setActiveSection}
      />
      <HeroSection 
        active={activeSection === 'hero'}
        onActivate={() => setActiveSection('hero')}
      />
      <AboutSection 
        active={activeSection === 'about'}
        onActivate={() => setActiveSection('about')}
      />
    </main>
  );
};
```

### 2. Event System for Loose Coupling

```tsx
// Custom event system
const useEventBus = () => {
  const emit = useCallback((event: string, data?: any) => {
    window.dispatchEvent(new CustomEvent(event, { detail: data }));
  }, []);
  
  const on = useCallback((event: string, handler: (data: any) => void) => {
    const listener = (e: CustomEvent) => handler(e.detail);
    window.addEventListener(event, listener);
    return () => window.removeEventListener(event, listener);
  }, []);
  
  return { emit, on };
};

// Usage
const PortfolioSection = () => {
  const { emit } = useEventBus();
  
  const handleProjectSelect = (project: PortfolioItem) => {
    emit('project:selected', project);
  };
  
  return (
    <section>
      {/* Portfolio content */}
    </section>
  );
};

const ContactSection = () => {
  const { on } = useEventBus();
  const [selectedProject, setSelectedProject] = useState<PortfolioItem | null>(null);
  
  useEffect(() => {
    return on('project:selected', setSelectedProject);
  }, [on]);
  
  return (
    <section>
      {selectedProject && (
        <Typography>Interested in {selectedProject.title}?</Typography>
      )}
    </section>
  );
};
```

## Performance Optimization Strategies

### 1. Component Memoization

```tsx
// Memoize expensive components
const TestimonialCard = memo(({ testimonial }: TestimonialCardProps) => {
  return (
    <Card variant="testimonial">
      {/* Card content */}
    </Card>
  );
});

// Memoize with custom comparison
const PortfolioGrid = memo(({ items }: PortfolioGridProps) => {
  return (
    <div className="grid">
      {items.map(item => (
        <PortfolioCard key={item.id} item={item} />
      ))}
    </div>
  );
}, (prevProps, nextProps) => {
  return prevProps.items.length === nextProps.items.length &&
         prevProps.items.every((item, index) => item.id === nextProps.items[index].id);
});
```

### 2. Lazy Loading

```tsx
// Lazy load below-the-fold sections
const TestimonialsSection = lazy(() => import('./sections/TestimonialsSection'));
const ContactSection = lazy(() => import('./sections/ContactSection'));

const HomePage = () => {
  return (
    <main>
      <HeroSection />
      <AboutSection />
      
      <Suspense fallback={<div>Loading...</div>}>
        <TestimonialsSection />
        <ContactSection />
      </Suspense>
    </main>
  );
};
```

### 3. Intersection Observer for Animations

```tsx
const useInView = (threshold = 0.1) => {
  const [inView, setInView] = useState(false);
  const ref = useRef<HTMLElement>(null);
  
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => setInView(entry.isIntersecting),
      { threshold }
    );
    
    if (ref.current) observer.observe(ref.current);
    
    return () => observer.disconnect();
  }, [threshold]);
  
  return { ref, inView };
};

// Usage in component
const AnimatedSection = ({ children }) => {
  const { ref, inView } = useInView();
  
  return (
    <section 
      ref={ref}
      className={`transition-opacity duration-1000 ${
        inView ? 'opacity-100' : 'opacity-0'
      }`}
    >
      {children}
    </section>
  );
};
```

## Error Boundaries and Fallbacks

### 1. Section-Level Error Boundaries

```tsx
class SectionErrorBoundary extends Component<
  { children: ReactNode; fallback?: ReactNode },
  { hasError: boolean }
> {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }
  
  static getDerivedStateFromError(error: Error) {
    return { hasError: true };
  }
  
  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="error-fallback">
          <Typography variant="heading">Something went wrong</Typography>
        </div>
      );
    }
    
    return this.props.children;
  }
}

// Usage
const HomePage = () => {
  return (
    <main>
      <SectionErrorBoundary>
        <HeroSection />
      </SectionErrorBoundary>
      
      <SectionErrorBoundary fallback={<AboutSectionFallback />}>
        <AboutSection />
      </SectionErrorBoundary>
    </main>
  );
};
```

## Testing Strategy for Composition

### 1. Component Integration Tests

```tsx
// Test component composition
describe('HeroSection Integration', () => {
  it('should integrate with HeroShapes without conflicts', () => {
    render(
      <HeroSection 
        title="PerPixel"
        subtitle="Agency"
        ctaButtons={[
          { variant: 'primary', children: 'Portfolio' },
          { variant: 'secondary', children: 'Hire me' }
        ]}
      />
    );
    
    // Test that HeroShapes animations are present
    expect(screen.getByAltText('floating circle')).toBeInTheDocument();
    expect(screen.getByAltText('floating cube')).toBeInTheDocument();
    
    // Test that new content is rendered
    expect(screen.getByText('PerPixel')).toBeInTheDocument();
    expect(screen.getByText('Portfolio')).toBeInTheDocument();
  });
});
```

### 2. Data Flow Tests

```tsx
// Test props down, events up pattern
describe('Portfolio Section Data Flow', () => {
  it('should handle project selection correctly', () => {
    const mockOnSelect = jest.fn();
    const projects = [{ id: '1', title: 'Project 1' }];
    
    render(
      <PortfolioSection 
        projects={projects}
        onProjectSelect={mockOnSelect}
      />
    );
    
    fireEvent.click(screen.getByText('Project 1'));
    expect(mockOnSelect).toHaveBeenCalledWith('1');
  });
});
```

This composition strategy ensures maintainable, performant, and testable component architecture while preserving existing functionality.
