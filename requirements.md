# Technical Requirements - PerPixel Project Integration

## Project Overview
Integration of Figma-designed homepage into existing Next.js/TypeScript project while preserving current components and animations.

## Components to Preserve (CRITICAL - DO NOT MODIFY)

### 1. HeroShapes.tsx
- **Location**: `src/components/Hero Section/HeroShapes.tsx`
- **Purpose**: Animated floating shapes with complex animations
- **Dependencies**: Custom CSS animations in globals.css
- **Requirements**: Must remain completely intact and functional
- **Integration**: Should work harmoniously with new design elements

### 2. Animation Keyframes (globals.css)
- **Location**: `src/app/globals.css` (lines 4-52)
- **Animations to Preserve**:
  - `@keyframes float` - Basic floating animation
  - `@keyframes float-reverse` - Reverse floating animation  
  - `@keyframes float-delayed` - Delayed floating animation
  - `@keyframes spin-slow` - Slow rotation animation
  - `@keyframes spin-reverse` - Reverse rotation animation
  - `@keyframes bounce-slow` - Slow bounce animation
- **CSS Classes to Preserve**:
  - `.animate-float`
  - `.animate-float-reverse`
  - `.animate-float-delayed`
  - `.animate-spin-slow`
  - `.animate-spin-reverse`
  - `.animate-bounce-slow`

### 3. Existing Project Structure
- **Next.js Configuration**: `next.config.ts`
- **TypeScript Configuration**: `tsconfig.json`
- **Tailwind Configuration**: `postcss.config.mjs`
- **Package Dependencies**: Current package.json structure
- **App Router Structure**: `src/app/` directory structure

## TypeScript Conversion Requirements

### 1. Component Type Definitions
- **Props Interfaces**: Define proper TypeScript interfaces for all component props
- **Component Types**: Use `React.FC` or function component typing
- **Event Handlers**: Properly type all event handlers and callbacks
- **State Management**: Type all useState and useEffect hooks properly

### 2. Image and Asset Types
- **Next.js Image**: Convert all `<img>` tags to Next.js `<Image>` components
- **Asset Imports**: Properly type imported assets and images
- **Background Images**: Convert CSS background images to Next.js optimized approach

### 3. Font Integration
- **Google Fonts**: Integrate required fonts through Next.js font optimization
- **Font Variables**: Create CSS custom properties for font families
- **Font Loading**: Implement proper font loading strategies

## Next.js Integration Specifications

### 1. App Router Compliance
- **File Structure**: Follow Next.js 13+ app router conventions
- **Server Components**: Use server components where appropriate
- **Client Components**: Mark interactive components with "use client"
- **Metadata**: Implement proper metadata for SEO

### 2. Performance Optimization
- **Image Optimization**: Use Next.js Image component with proper sizing
- **Code Splitting**: Implement dynamic imports for large components
- **Lazy Loading**: Implement lazy loading for below-the-fold content
- **Bundle Optimization**: Minimize bundle size through proper imports

### 3. Routing and Navigation
- **Internal Links**: Use Next.js Link component for internal navigation
- **Dynamic Routes**: Prepare structure for potential dynamic routing
- **Navigation State**: Implement proper navigation state management

## Tailwind CSS Compatibility

### 1. Design System Integration
- **Color Palette**: Define custom colors in Tailwind config
  - Primary Orange: `#feb172`
  - Secondary Pink: `#fadcd9`
  - Accent Pink: `#f8afa6`
  - Dark: `#131d26`
- **Typography Scale**: Define custom font sizes for large display text
- **Spacing Scale**: Define custom spacing for large layouts

### 2. Responsive Design
- **Breakpoint Strategy**: Define responsive breakpoints for the design
- **Mobile-First**: Convert fixed-width design to responsive approach
- **Container Strategy**: Implement proper container and max-width handling

### 3. Custom Utilities
- **Animation Classes**: Integrate existing animations with Tailwind
- **Custom Components**: Create Tailwind component classes for repeated patterns
- **Utility Extensions**: Extend Tailwind with custom utilities as needed

## Component Architecture Requirements

### 1. Modular Structure
- **Single Responsibility**: Each component should have a single, clear purpose
- **Reusability**: Components should be reusable across different sections
- **Composition**: Use component composition over inheritance
- **Props Interface**: Clear, well-documented props for each component

### 2. Component Hierarchy
```
src/components/
├── layout/
│   ├── Header.tsx
│   ├── Footer.tsx
│   └── Navigation.tsx
├── sections/
│   ├── HeroSection.tsx
│   ├── AboutSection.tsx
│   ├── PortfolioSection.tsx
│   ├── TestimonialsSection.tsx
│   └── ContactSection.tsx
├── ui/
│   ├── Button.tsx
│   ├── Card.tsx
│   ├── Typography.tsx
│   └── SocialLinks.tsx
└── Hero Section/ (PRESERVE EXISTING)
    ├── Hero.tsx
    ├── HeroShapes.tsx
    └── PortfolioGrid.tsx
```

### 3. State Management
- **Local State**: Use useState for component-specific state
- **Shared State**: Consider Context API for shared state if needed
- **Form State**: Implement proper form state management for contact forms

## Performance and Accessibility Requirements

### 1. Performance Targets
- **Core Web Vitals**: Meet Google's Core Web Vitals standards
- **First Contentful Paint**: < 1.5 seconds
- **Largest Contentful Paint**: < 2.5 seconds
- **Cumulative Layout Shift**: < 0.1

### 2. Accessibility Standards
- **WCAG 2.1 AA**: Meet WCAG 2.1 AA compliance standards
- **Semantic HTML**: Use proper semantic HTML elements
- **ARIA Labels**: Implement proper ARIA labels for interactive elements
- **Keyboard Navigation**: Ensure full keyboard accessibility
- **Screen Reader**: Optimize for screen reader compatibility

### 3. SEO Requirements
- **Meta Tags**: Implement proper meta tags and Open Graph
- **Structured Data**: Add structured data markup where appropriate
- **Alt Text**: Provide descriptive alt text for all images
- **Heading Hierarchy**: Maintain proper heading hierarchy (h1-h6)

## Integration Constraints

### 1. Existing Code Preservation
- **No Breaking Changes**: Must not break existing functionality
- **Animation Compatibility**: New components must not interfere with existing animations
- **Style Isolation**: Prevent CSS conflicts between old and new components

### 2. Development Workflow
- **TypeScript Strict**: Maintain strict TypeScript configuration
- **ESLint Rules**: Follow existing ESLint configuration
- **Code Formatting**: Maintain consistent code formatting standards

### 3. Testing Requirements
- **Component Testing**: Each new component should be testable
- **Integration Testing**: Test integration between old and new components
- **Visual Regression**: Ensure no visual regressions in existing components

## Migration Strategy

### 1. Incremental Approach
- **Phase-by-Phase**: Migrate one section at a time
- **Backward Compatibility**: Maintain backward compatibility during migration
- **Rollback Plan**: Ability to rollback changes if issues arise

### 2. Quality Assurance
- **Code Review**: All changes require thorough code review
- **Testing**: Comprehensive testing before integration
- **Performance Monitoring**: Monitor performance impact of changes

### 3. Documentation
- **Component Documentation**: Document all new components
- **Integration Guide**: Provide integration guide for future developers
- **Maintenance Guide**: Document maintenance procedures for the integrated system
