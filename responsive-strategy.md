# Responsive Breakpoint Strategy - PerPixel Project

## Overview
The Figma design is built for a fixed width of 1440px. This document outlines the strategy for converting it to a responsive design that works across all device sizes while maintaining design integrity.

## Breakpoint System

### Custom Breakpoints
- **xs**: 480px - Extra small devices (small phones)
- **sm**: 640px - Small devices (phones)
- **md**: 768px - Medium devices (tablets)
- **lg**: 1024px - Large devices (small laptops)
- **xl**: 1280px - Extra large devices (laptops)
- **2xl**: 1440px - Design target width (desktop)
- **3xl**: 1600px - Ultra wide displays

### Container Strategy
- **Mobile (xs-sm)**: Full width with padding
- **Tablet (md)**: Max width 768px, centered
- **Laptop (lg)**: Max width 1024px, centered
- **Desktop (xl)**: Max width 1280px, centered
- **Large Desktop (2xl+)**: Max width 1440px, centered

## Typography Scaling Strategy

### Display Text Scaling
```css
/* Desktop (2xl+): Original sizes */
--font-size-display-xl: 15rem;     /* "PerPixel" */
--font-size-display-lg: 12rem;     /* "Agency" */
--font-size-display-md: 19.375rem; /* "Creative" */

/* Laptop (xl): 80% scale */
--font-size-display-xl-lg: 12rem;
--font-size-display-lg-lg: 9.6rem;
--font-size-display-md-lg: 15.5rem;

/* Tablet (md): 60% scale */
--font-size-display-xl-md: 9rem;
--font-size-display-lg-md: 7.2rem;
--font-size-display-md-md: 11.625rem;

/* Mobile (sm): 40% scale */
--font-size-display-xl-sm: 6rem;
--font-size-display-lg-sm: 4.8rem;
--font-size-display-md-sm: 7.75rem;
```

### Content Text Scaling
- **Desktop**: Original sizes (16px-48px range)
- **Tablet**: 90% scale
- **Mobile**: 85% scale with increased line height

## Layout Adaptation Strategy

### Grid System
- **Desktop**: Multi-column layouts as designed
- **Tablet**: Reduce to 2-column layouts
- **Mobile**: Single column, stacked layout

### Card Layouts
- **Desktop**: Original card sizes and spacing
- **Tablet**: Reduce card sizes by 20%, maintain proportions
- **Mobile**: Full-width cards with reduced padding

### Navigation
- **Desktop**: Horizontal navigation bar
- **Tablet**: Horizontal with reduced spacing
- **Mobile**: Hamburger menu or vertical stack

## Component-Specific Responsive Behavior

### Hero Section
- **Desktop**: Large display text with floating shapes
- **Tablet**: Reduced text size, maintain shape animations
- **Mobile**: Stacked layout, simplified shapes

### Portfolio Grid
- **Desktop**: 3-4 column grid
- **Tablet**: 2 column grid
- **Mobile**: Single column

### Testimonials
- **Desktop**: 3 column layout
- **Tablet**: 2 column layout
- **Mobile**: Single column with horizontal scroll option

### Contact Section
- **Desktop**: Multi-column card layout
- **Tablet**: 2 column layout
- **Mobile**: Stacked cards

## Image and Media Strategy

### Background Images
- Use CSS `background-size: cover` with proper positioning
- Provide multiple image sizes for different breakpoints
- Implement lazy loading for performance

### Next.js Image Optimization
- Convert all images to Next.js Image component
- Define responsive sizes for each breakpoint
- Use proper aspect ratios

## Animation Adaptations

### Existing HeroShapes
- **Desktop**: Full animations as designed
- **Tablet**: Reduced animation intensity
- **Mobile**: Simplified animations or static positioning

### New Animations
- Use `prefers-reduced-motion` media query
- Scale animation distances based on screen size
- Maintain performance on mobile devices

## Performance Considerations

### Mobile Optimization
- Reduce animation complexity on mobile
- Implement intersection observer for scroll animations
- Use CSS transforms for better performance
- Minimize layout shifts

### Loading Strategy
- Critical CSS for above-the-fold content
- Lazy load below-the-fold sections
- Progressive image loading

## Implementation Guidelines

### CSS Approach
```css
/* Mobile-first approach */
.hero-title {
  font-size: var(--font-size-display-xl-sm);
}

@media (min-width: 768px) {
  .hero-title {
    font-size: var(--font-size-display-xl-md);
  }
}

@media (min-width: 1280px) {
  .hero-title {
    font-size: var(--font-size-display-xl-lg);
  }
}

@media (min-width: 1440px) {
  .hero-title {
    font-size: var(--font-size-display-xl);
  }
}
```

### Tailwind Utilities
- Use responsive prefixes: `sm:`, `md:`, `lg:`, `xl:`, `2xl:`
- Create custom responsive utilities for large display text
- Implement container queries where beneficial

## Testing Strategy

### Device Testing
- iPhone SE (375px)
- iPhone 12 Pro (390px)
- iPad (768px)
- iPad Pro (1024px)
- MacBook Air (1280px)
- Desktop (1440px+)

### Browser Testing
- Chrome, Firefox, Safari, Edge
- Test responsive behavior and animations
- Verify touch interactions on mobile

## Accessibility Considerations

### Responsive Accessibility
- Maintain proper touch target sizes (44px minimum)
- Ensure readable text at all sizes
- Test keyboard navigation at all breakpoints
- Verify screen reader compatibility

### Motion Accessibility
- Respect `prefers-reduced-motion`
- Provide alternative static layouts
- Ensure content is accessible without animations
