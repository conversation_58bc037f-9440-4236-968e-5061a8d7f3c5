import React from 'react';
import { TypographyProps } from '@/types';

/**
 * Typography component with responsive scaling following PerPixel design system
 * 
 * @param variant - Typography variant (display-xl, display-lg, etc.)
 * @param children - Text content
 * @param className - Additional CSS classes
 * @param as - HTML element to render as
 * @param color - Text color variant
 */
const Typography: React.FC<TypographyProps> = ({
  variant = 'body',
  children,
  className = '',
  as,
  color = 'default',
}) => {
  // Color variants
  const colorStyles = {
    default: 'text-current',
    primary: 'text-[#feb172]',
    secondary: 'text-[#fadcd9]',
    muted: 'text-[#344053]',
    light: 'text-white',
    dark: 'text-black',
  };

  // Variant styles with responsive scaling
  const variantStyles = {
    'display-xl': {
      element: 'h1',
      classes: `
        text-6xl sm:text-8xl md:text-9xl lg:text-[12rem] xl:text-[15rem]
        font-extrabold leading-none tracking-tight
      `,
    },
    'display-lg': {
      element: 'h1',
      classes: `
        text-5xl sm:text-6xl md:text-7xl lg:text-[9.6rem] xl:text-[12rem]
        font-extrabold leading-none tracking-tight
      `,
    },
    'display-md': {
      element: 'h1',
      classes: `
        text-7xl sm:text-8xl md:text-[11.625rem] lg:text-[15.5rem] xl:text-[19.375rem]
        font-bold leading-none tracking-tight
      `,
    },
    'display-sm': {
      element: 'h1',
      classes: `
        text-6xl sm:text-7xl md:text-8xl lg:text-[12.5rem] xl:text-[15.625rem]
        font-bold leading-none tracking-tight
      `,
    },
    'hero': {
      element: 'h2',
      classes: `
        text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-[7.5rem]
        font-bold leading-none tracking-wide
      `,
    },
    'section-header': {
      element: 'h2',
      classes: `
        text-3xl sm:text-4xl md:text-5xl lg:text-[5.063rem]
        font-normal leading-tight
      `,
    },
    'heading': {
      element: 'h3',
      classes: `
        text-2xl sm:text-3xl md:text-4xl lg:text-[3rem]
        font-black leading-tight tracking-wide
      `,
    },
    'subheading': {
      element: 'h4',
      classes: `
        text-xl sm:text-2xl md:text-3xl lg:text-[2.75rem]
        font-semibold leading-tight
      `,
    },
    'body-lg': {
      element: 'p',
      classes: `
        text-lg sm:text-xl md:text-[1.375rem]
        font-normal leading-relaxed
      `,
    },
    'body': {
      element: 'p',
      classes: `
        text-base sm:text-lg md:text-base
        font-normal leading-relaxed
      `,
    },
    'caption': {
      element: 'span',
      classes: `
        text-sm sm:text-[0.9375rem]
        font-light leading-normal uppercase tracking-wide
      `,
    },
  };

  const variantConfig = variantStyles[variant];
  const Element = as || variantConfig.element;

  // Combine all styles
  const typographyClasses = `
    ${variantConfig.classes}
    ${colorStyles[color]}
    ${className}
  `.replace(/\s+/g, ' ').trim();

  return React.createElement(
    Element,
    { className: typographyClasses },
    children
  );
};

export default Typography;

// Specialized typography components for common use cases
export const DisplayText: React.FC<Omit<TypographyProps, 'variant'> & { 
  size: 'xl' | 'lg' | 'md' | 'sm' 
}> = ({ size, ...props }) => (
  <Typography variant={`display-${size}` as any} {...props} />
);

export const SectionHeader: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (
  <Typography variant="section-header" {...props} />
);

export const Heading: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (
  <Typography variant="heading" {...props} />
);

export const Subheading: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (
  <Typography variant="subheading" {...props} />
);

export const BodyText: React.FC<Omit<TypographyProps, 'variant'> & { 
  size?: 'lg' | 'default' 
}> = ({ size = 'default', ...props }) => (
  <Typography variant={size === 'lg' ? 'body-lg' : 'body'} {...props} />
);

export const Caption: React.FC<Omit<TypographyProps, 'variant'>> = (props) => (
  <Typography variant="caption" {...props} />
);

// Special components for PerPixel branding
export const PerPixelTitle: React.FC<{ className?: string }> = ({ className = '' }) => (
  <DisplayText 
    size="xl" 
    color="dark"
    className={`select-none ${className}`}
    style={{ fontFamily: 'Georgia, serif' }}
  >
    PerPixel
  </DisplayText>
);

export const AgencySubtitle: React.FC<{ className?: string }> = ({ className = '' }) => (
  <DisplayText 
    size="lg" 
    color="light"
    className={`select-none ${className}`}
    style={{ fontFamily: 'Georgia, serif' }}
  >
    Agency
  </DisplayText>
);

export const CreativeText: React.FC<{ className?: string }> = ({ className = '' }) => (
  <DisplayText 
    size="md" 
    color="dark"
    className={`select-none ${className}`}
  >
    Creative
  </DisplayText>
);

export const DesignText: React.FC<{ className?: string }> = ({ className = '' }) => (
  <DisplayText 
    size="sm" 
    color="light"
    className={`select-none ${className}`}
  >
    Design
  </DisplayText>
);

// Testimonial-specific typography
export const TestimonialTitle: React.FC<{ children: React.ReactNode; theme?: 'light' | 'dark' }> = ({ 
  children, 
  theme = 'light' 
}) => (
  <Typography 
    variant="subheading" 
    color={theme === 'light' ? 'dark' : 'light'}
    className="mb-4"
  >
    "{children}"
  </Typography>
);

export const TestimonialContent: React.FC<{ children: React.ReactNode; theme?: 'light' | 'dark' }> = ({ 
  children, 
  theme = 'light' 
}) => (
  <Typography 
    variant="caption" 
    color={theme === 'light' ? 'dark' : 'light'}
    className="leading-relaxed"
    as="p"
  >
    {children}
  </Typography>
);

// Portfolio-specific typography
export const PortfolioTitle: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <Typography 
    variant="hero" 
    color="light"
    className="select-none"
  >
    {children}
  </Typography>
);

export const ProjectTitle: React.FC<{ children: React.ReactNode; featured?: boolean }> = ({ 
  children, 
  featured = false 
}) => (
  <Typography 
    variant={featured ? 'subheading' : 'heading'}
    color={featured ? 'light' : 'dark'}
    className="font-medium"
  >
    {children}
  </Typography>
);

// Year display for portfolio
export const YearDisplay: React.FC<{ year: string | number }> = ({ year }) => (
  <Typography 
    variant="hero"
    color="light"
    className="font-bold leading-none select-none"
    style={{ fontFamily: 'Glegoo, serif' }}
  >
    {year}
  </Typography>
);
