/**
 * TypeScript interfaces and types for PerPixel project components
 * This file contains all component prop interfaces and shared types
 */

import { ReactNode } from 'react';

// ============================================================================
// SHARED TYPES
// ============================================================================

export type ButtonVariant = 'primary' | 'secondary' | 'cta' | 'link';
export type ButtonSize = 'sm' | 'md' | 'lg';
export type CardVariant = 'default' | 'testimonial' | 'portfolio' | 'contact' | 'introduction';
export type CardTheme = 'light' | 'dark';
export type TypographyVariant = 
  | 'display-xl' | 'display-lg' | 'display-md' | 'display-sm'
  | 'hero' | 'section-header' | 'heading' | 'subheading'
  | 'body-lg' | 'body' | 'caption';

// ============================================================================
// DATA INTERFACES
// ============================================================================

export interface TestimonialData {
  id: string;
  name: string;
  company?: string;
  content: string;
  rating?: number;
  image?: string;
  theme?: CardTheme;
}

export interface PortfolioItem {
  id: string;
  title: string;
  description?: string;
  image?: string;
  category?: string;
  featured?: boolean;
}

export interface SocialLink {
  platform: 'instagram' | 'twitter' | 'linkedin' | 'facebook';
  url: string;
  label?: string;
}

export interface NavigationItem {
  label: string;
  href: string;
  external?: boolean;
}

// ============================================================================
// UI COMPONENT INTERFACES
// ============================================================================

export interface ButtonProps {
  variant?: ButtonVariant;
  size?: ButtonSize;
  children: ReactNode;
  onClick?: () => void;
  href?: string;
  disabled?: boolean;
  className?: string;
  icon?: ReactNode;
  iconPosition?: 'left' | 'right';
}

export interface CardProps {
  variant?: CardVariant;
  theme?: CardTheme;
  children: ReactNode;
  className?: string;
  rounded?: boolean;
  padding?: 'sm' | 'md' | 'lg';
}

export interface TypographyProps {
  variant?: TypographyVariant;
  children: ReactNode;
  className?: string;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span' | 'div';
  color?: 'default' | 'primary' | 'secondary' | 'muted' | 'light' | 'dark';
}

export interface ImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  fill?: boolean;
  sizes?: string;
}

export interface SocialLinksProps {
  links: SocialLink[];
  variant?: 'horizontal' | 'vertical';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

// ============================================================================
// LAYOUT COMPONENT INTERFACES
// ============================================================================

export interface ContainerProps {
  children: ReactNode;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  className?: string;
  padding?: boolean;
}

export interface HeaderProps {
  navigation?: NavigationItem[];
  logo?: {
    src: string;
    alt: string;
    href?: string;
  };
  ctaButton?: ButtonProps;
  className?: string;
}

export interface FooterProps {
  socialLinks?: SocialLink[];
  navigation?: NavigationItem[];
  copyright?: string;
  className?: string;
}

export interface NavigationProps {
  items: NavigationItem[];
  variant?: 'horizontal' | 'vertical' | 'mobile';
  className?: string;
}

// ============================================================================
// SECTION COMPONENT INTERFACES
// ============================================================================

export interface HeroSectionProps {
  title: string;
  subtitle: string;
  ctaButtons?: ButtonProps[];
  backgroundImage?: string;
  testimonial?: {
    content: string;
    rating?: number;
  };
  experience?: {
    years: number;
    description: string;
  };
  className?: string;
}

export interface AboutSectionProps {
  title?: string;
  introduction?: {
    title: string;
    content: string;
  };
  contact?: {
    title: string;
    methods: Array<{
      type: string;
      value: string;
      icon?: ReactNode;
    }>;
  };
  className?: string;
}

export interface PortfolioSectionProps {
  title?: string;
  featured?: PortfolioItem;
  projects?: PortfolioItem[];
  quickLinks?: Array<{
    title: string;
    href: string;
    icon?: ReactNode;
  }>;
  className?: string;
}

export interface TestimonialsSectionProps {
  title?: string;
  testimonials: TestimonialData[];
  ctaButton?: ButtonProps;
  className?: string;
}

export interface ContactSectionProps {
  title?: string;
  description?: string;
  contactInfo?: {
    email?: string;
    phone?: string;
    address?: string;
  };
  socialLinks?: SocialLink[];
  className?: string;
}

// ============================================================================
// SPECIALIZED COMPONENT INTERFACES
// ============================================================================

export interface TestimonialCardProps {
  testimonial: TestimonialData;
  variant?: 'default' | 'featured';
  showImage?: boolean;
  showRating?: boolean;
  className?: string;
}

export interface PortfolioCardProps {
  item: PortfolioItem;
  variant?: 'grid' | 'featured' | 'list';
  showDescription?: boolean;
  onClick?: (item: PortfolioItem) => void;
  className?: string;
}

export interface ProjectListProps {
  projects: Array<{
    name: string;
    status?: 'active' | 'completed' | 'upcoming';
  }>;
  className?: string;
}

export interface QuickLinksProps {
  links: Array<{
    title: string;
    href: string;
    icon?: ReactNode;
  }>;
  variant?: 'grid' | 'list';
  className?: string;
}

// ============================================================================
// FORM INTERFACES
// ============================================================================

export interface ContactFormProps {
  onSubmit: (data: ContactFormData) => void;
  loading?: boolean;
  className?: string;
}

export interface ContactFormData {
  name: string;
  email: string;
  message: string;
  subject?: string;
}

// ============================================================================
// ANIMATION INTERFACES
// ============================================================================

export interface AnimationProps {
  delay?: number;
  duration?: number;
  easing?: string;
  trigger?: 'hover' | 'scroll' | 'click' | 'load';
}

export interface FloatingShapeProps {
  shape: 'circle' | 'cube';
  size: 'sm' | 'md' | 'lg';
  position: {
    top?: string;
    bottom?: string;
    left?: string;
    right?: string;
  };
  animation?: 'float' | 'float-reverse' | 'float-delayed' | 'spin-slow' | 'spin-reverse';
  className?: string;
}

// ============================================================================
// RESPONSIVE INTERFACES
// ============================================================================

export interface ResponsiveProps {
  mobile?: any;
  tablet?: any;
  desktop?: any;
}

export interface BreakpointProps {
  xs?: any;
  sm?: any;
  md?: any;
  lg?: any;
  xl?: any;
  '2xl'?: any;
}

// ============================================================================
// THEME INTERFACES
// ============================================================================

export interface ThemeProps {
  colors?: {
    primary?: string;
    secondary?: string;
    accent?: string;
    background?: string;
    foreground?: string;
  };
  fonts?: {
    sans?: string;
    serif?: string;
    mono?: string;
  };
  spacing?: {
    xs?: string;
    sm?: string;
    md?: string;
    lg?: string;
    xl?: string;
  };
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type ComponentWithChildren<T = {}> = T & {
  children: ReactNode;
};

export type OptionalClassName<T = {}> = T & {
  className?: string;
};

export type WithVariant<T extends string> = {
  variant?: T;
};

export type WithTheme = {
  theme?: CardTheme;
};

export type WithSize<T extends string> = {
  size?: T;
};

// ============================================================================
// LEGACY COMPONENT INTERFACES (for existing components)
// ============================================================================

export interface HeroShapesProps {
  // This component should not be modified - no props interface needed
  // Keeping for documentation purposes
}

export interface LegacyHeroProps {
  // Interface for existing Hero.tsx component
  // To be used during transition period
}

export interface LegacyPortfolioGridProps {
  // Interface for existing PortfolioGrid.tsx component
  // To be used during transition period
}
