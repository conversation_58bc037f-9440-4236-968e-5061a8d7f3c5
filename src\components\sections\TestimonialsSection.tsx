import React from 'react';
import { TestimonialsSectionProps } from '../../types';
import { SectionContainer } from '../layout/Container';
import {
  SectionHeader,
  BodyText
} from '../ui/Typography';
import { TestimonialCard } from '../ui/Card';
import Button from '../ui/Button';

/**
 * Testimonials section component based on Figma design
 * Features three-column testimonial layout with mixed themes and CTA button
 */
const TestimonialsSection: React.FC<TestimonialsSectionProps> = ({
  title = "Testimonials",
  testimonials = [
    {
      id: '1',
      name: '<PERSON>',
      company: 'Tech Innovations Inc.',
      content: 'Amazing Team with Lorem Ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet.',
      rating: 5,
      theme: 'light'
    },
    {
      id: '2',
      name: '<PERSON>',
      company: 'Design Studio Pro',
      content: 'Good Dreams for lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet.',
      rating: 5,
      theme: 'dark'
    },
    {
      id: '3',
      name: '<PERSON> Rodriguez',
      company: 'Creative Agency',
      content: 'Big Dreams for lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet.',
      rating: 5,
      theme: 'dark'
    }
  ],
  ctaButton = {
    variant: 'secondary' as const,
    children: 'See All Reviews',
    href: '/testimonials'
  },
  className = '',
}) => {
  return (
    <SectionContainer background="default" className={className}>
      {/* Section Header */}
      <div className="text-center mb-16">
        <SectionHeader color="dark" className="mb-8">
          {title}
        </SectionHeader>
        
        {/* Decorative quote and description */}
        <div className="max-w-4xl mx-auto">
          <div className="relative mb-6">
            {/* Large decorative quote mark */}
            <div className="flex justify-center mb-4">
              <div className="w-16 h-16 bg-primary-orange/10 rounded-full flex items-center justify-center">
                <span className="text-3xl text-primary-orange">"</span>
              </div>
            </div>
            
            <BodyText size="lg" color="muted" className="text-center leading-relaxed">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum
              dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit
              amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet,
              consectetur adipiscing elit.
            </BodyText>
          </div>
        </div>
      </div>

      {/* Testimonials Grid - First Row */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        {/* First testimonial with image */}
        <div className="space-y-0">
          {/* Image placeholder */}
          <div className="w-full h-64 bg-gradient-to-br from-secondary-peach to-primary-orange/30 rounded-t-2xl mb-0">
            <div className="w-full h-full flex items-center justify-center">
              <span className="text-gray-600">Client Photo</span>
            </div>
          </div>
          
          {/* Testimonial card attached to image */}
          <TestimonialCard
            testimonial={testimonials[0]}
            theme="light"
            className="rounded-t-none border-t-0"
          />
        </div>

        {/* Second testimonial - dark theme */}
        <div>
          <TestimonialCard
            testimonial={testimonials[1]}
            theme="dark"
            className="h-full"
          />
        </div>

        {/* Third testimonial with image */}
        <div className="space-y-0">
          {/* Image placeholder */}
          <div className="w-full h-64 bg-gradient-to-br from-primary-orange/20 to-accent-pink/40 rounded-t-2xl mb-0">
            <div className="w-full h-full flex items-center justify-center">
              <span className="text-gray-600">Client Photo</span>
            </div>
          </div>
          
          {/* Testimonial card attached to image */}
          <TestimonialCard
            testimonial={testimonials[2]}
            theme="light"
            className="rounded-t-none border-t-0"
          />
        </div>
      </div>

      {/* Testimonials Grid - Second Row (Staggered) */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
        {/* Dark testimonial */}
        <div className="lg:col-start-1">
          <TestimonialCard
            testimonial={{
              id: '4',
              name: 'David Park',
              company: 'Innovation Labs',
              content: 'Big Dreams for lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet.',
              rating: 5,
              theme: 'dark'
            }}
            theme="dark"
            className="h-full"
          />
        </div>

        {/* Center image */}
        <div className="lg:col-start-2">
          <div className="w-full h-64 lg:h-full bg-gradient-to-br from-accent-pink/30 to-secondary-peach/50 rounded-2xl flex items-center justify-center">
            <span className="text-gray-600">Featured Image</span>
          </div>
        </div>

        {/* Light testimonial */}
        <div className="lg:col-start-3">
          <TestimonialCard
            testimonial={{
              id: '5',
              name: 'Lisa Wang',
              company: 'Digital Creatives',
              content: 'Good Dreams for lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet.',
              rating: 5,
              theme: 'light'
            }}
            theme="light"
            className="h-full"
          />
        </div>
      </div>

      {/* CTA Button */}
      <div className="text-center">
        <div className="inline-block border-2 border-accent-pink rounded-lg p-1">
          <Button
            variant="link"
            className="px-8 py-4 text-lg font-semibold text-dark-bg hover:text-primary-orange"
            {...ctaButton}
          >
            {ctaButton.children}
          </Button>
        </div>
      </div>
    </SectionContainer>
  );
};

export default TestimonialsSection;

// Simplified testimonials section variant
export const SimpleTestimonialsSection: React.FC<{
  testimonials: Array<{
    name: string;
    company?: string;
    content: string;
    rating?: number;
  }>;
  className?: string;
}> = ({ testimonials, className = '' }) => (
  <SectionContainer background="secondary" className={className}>
    <div className="text-center mb-12">
      <SectionHeader color="dark">What Our Clients Say</SectionHeader>
    </div>
    
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {testimonials.map((testimonial, index) => (
        <TestimonialCard
          key={index}
          testimonial={{
            id: index.toString(),
            ...testimonial,
            theme: index % 2 === 0 ? 'light' : 'dark'
          }}
          theme={index % 2 === 0 ? 'light' : 'dark'}
        />
      ))}
    </div>
  </SectionContainer>
);

// Featured testimonial section
export const FeaturedTestimonialSection: React.FC<{
  testimonial: {
    name: string;
    company: string;
    content: string;
    image?: string;
  };
  className?: string;
}> = ({ testimonial, className = '' }) => (
  <SectionContainer background="primary" className={className}>
    <div className="max-w-4xl mx-auto text-center">
      <div className="mb-8">
        <span className="text-6xl text-white/20">"</span>
      </div>
      
      <BodyText size="lg" color="light" className="mb-8 leading-relaxed">
        {testimonial.content}
      </BodyText>
      
      <div className="flex items-center justify-center space-x-4">
        {testimonial.image && (
          <div className="w-12 h-12 bg-white/20 rounded-full"></div>
        )}
        <div className="text-left">
          <div className="text-white font-semibold">{testimonial.name}</div>
          <div className="text-white/80 text-sm">{testimonial.company}</div>
        </div>
      </div>
    </div>
  </SectionContainer>
);
